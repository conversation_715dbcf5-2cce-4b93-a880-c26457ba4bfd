
// Implementation file (FaceScannerEnhanced.cpp)

#include "FaceScannerEnhanced.h"

FaceScannerEnhanced::FaceScannerEnhanced() {
  responseBuffer = nullptr;
  streamBuffer = nullptr;
  useStreamBuffer = false;
  encryptionEnabled = false;
  memset(encryptionKey, 0, sizeof(encryptionKey));
}




FaceScannerEnhanced::~FaceScannerEnhanced() {
  if (responseBuffer) {
      free(responseBuffer);
  }
  if (streamBuffer) {
      free(streamBuffer);
  }
}

bool FaceScannerEnhanced::begin() {
  // Try common baud rates in sequence
  uint32_t baudRates[] = {115200};
  bool success = false;
  
  for (int i = 0; i < sizeof(baudRates)/sizeof(baudRates[0]); i++) {
    Serial1.begin(baudRates[i], SERIAL_8N1, RXD1, TXD1);
    delay(100);
    Serial.print("Trying baud rate: ");
    Serial.println(baudRates[i]);
    

     
  // Allocate both regular and streaming buffers
  responseBuffer = (uint8_t*)malloc(MAX_RESPONSE_SIZE);
  streamBuffer = (uint8_t*)malloc(STREAM_BUFFER_SIZE);
  
    // Test communication with simple command
    if (sendCommand(MID_RESET, nullptr, 0)) {
      uint8_t status;
      if (waitForResponse(MID_GETSTATUS, &status, nullptr, 1000)) {
        success = true;
        Serial.print("✓ Communication established at ");
        Serial.print(baudRates[i]);
        Serial.println(" baud");
        break;
      }
    }
  }
  
  if (!success) {
    Serial.println("✗ Failed to establish communication at any baud rate");
    return true;
  }
 
  
  if (!responseBuffer || !streamBuffer) {
      Serial.println("✗ Buffer allocation failed");
      return false;
  }
  
  Serial.println("✓ FaceScanner buffers allocated successfully");
  return true;
}

void FaceScannerEnhanced::clearSerialBuffer() {
  while (Serial1.available()) {
      Serial1.read();
  }
}

bool FaceScannerEnhanced::reset() {
  Serial.println("Resetting face scanner module...");
  return sendCommand(MID_RESET, nullptr, 0) && 
         waitForResponse(MID_RESET, nullptr, nullptr, 5000);
}

bool FaceScannerEnhanced::getStatus(uint8_t* status) {
  if (!status) return false;
  
  if (sendCommand(MID_GETSTATUS, nullptr, 0)) {
      uint8_t response[10];
      uint16_t responseSize;
      if (waitForResponse(MID_GETSTATUS, response, &responseSize, 3000)) {
          if (responseSize >= 1) {
              *status = response[0];
              return true;
          }
      }
  }
  return false;
}

bool FaceScannerEnhanced::snapImage(uint8_t imageNumber, uint8_t quality) {
  uint8_t data[2] = {imageNumber, quality};
  return sendCommand(MID_SNAPIMAGE, data, 2) &&
         waitForResponse(MID_SNAPIMAGE, nullptr, nullptr, 10000);
}

bool FaceScannerEnhanced::getSavedImageSize(uint8_t imageNumber, uint32_t* imageSize) {
  if (!imageSize) return false;
  
  uint8_t data[1] = {imageNumber};
  if (!sendCommand(MID_GETSAVEDIMAGE, data, 1)) return false;
  
  uint8_t response[16];
  uint16_t responseSize;
  
  if (waitForResponse(MID_GETSAVEDIMAGE, response, &responseSize, 1000)) {
      Serial.print("Raw response size: ");
      Serial.println(responseSize);
      Serial.print("Raw response bytes: ");
      for (int i = 0; i < ((responseSize < 8) ? responseSize : 8); i++) {
          Serial.print("0x");
          if (response[i] < 16) Serial.print("0");
          Serial.print(response[i], HEX);
          Serial.print(" ");
      }
      Serial.println();
      
      if (responseSize >= 4) {
          // Use Big Endian 32-bit interpretation
          uint32_t bigEndian32 = ((uint32_t)response[0] << 24) |
                                 ((uint32_t)response[1] << 16) |
                                 ((uint32_t)response[2] << 8) |
                                 (uint32_t)response[3];
          
          *imageSize = bigEndian32;
          Serial.print("Image size (Big Endian 32-bit): ");
          Serial.print(*imageSize);
          Serial.println(" bytes");
          
          if (*imageSize > 0 && *imageSize < 500000) {
              Serial.println("✓ Image size validation passed");
              return true;
          } else {
              Serial.println("✗ Image size validation failed");
              return false;
          }
      } else {
          Serial.println("✗ Response too short for image size data");
      }
  } else {
      Serial.println("✗ No response received for getSavedImageSize");
  }
  
  return false;
}

bool FaceScannerEnhanced::uploadImage(uint32_t offset, uint32_t size, uint8_t* imageData, uint16_t* actualSize) {
  if (!imageData || !actualSize || size == 0 || size > MAX_CHUNK_SIZE) {
      Serial.println("✗ Invalid uploadImage parameters");
      return false;
  }
  
  // Pack parameters in Big Endian format
  uint8_t data[8];
  data[0] = (offset >> 24) & 0xFF;
  data[1] = (offset >> 16) & 0xFF;
  data[2] = (offset >> 8) & 0xFF;
  data[3] = offset & 0xFF;
  data[4] = (size >> 24) & 0xFF;
  data[5] = (size >> 16) & 0xFF;
  data[6] = (size >> 8) & 0xFF;
  data[7] = size & 0xFF;
  
  Serial.print("Upload request - Offset: ");
  Serial.print(offset);
  Serial.print(", Size: ");
  Serial.println(size);
  
  if (!sendCommand(MID_UPLOADIMAGE, data, 8)) {
      Serial.println("✗ Failed to send uploadImage command");
      return false;
  }
  
  // Use streaming response handler for large data
  if (waitForStreamResponse(MID_UPLOADIMAGE, imageData, size, 15000)) {
      *actualSize = size;
      Serial.print("✓ Stream upload successful - Received ");
      Serial.print(*actualSize);
      Serial.println(" bytes");
      return true;
  } else {
      Serial.println("✗ Stream upload failed");
      return false;
  }
}

// NEW METHODS IMPLEMENTATION

bool FaceScannerEnhanced::verify(uint8_t pdRightaway, uint8_t timeout, uint8_t verifyMode, VerifyResult* result) {
  if (!result) return false;
  
  uint8_t data[3] = {pdRightaway, timeout, verifyMode};
  
  Serial.print("→ Starting verification (timeout: ");
  Serial.print(timeout);
  Serial.print("s, mode: 0x");
  Serial.print(verifyMode, HEX);
  Serial.println(")");
  
  if (!sendCommand(MID_VERIFY, data, 3)) {
      Serial.println("✗ Failed to send verify command");
      return false;
  }
  
  uint8_t response[64];
  uint16_t responseSize;
  uint32_t verifyTimeout = (timeout > 0) ? (timeout * 1000) : 10000;
  
  if (waitForResponse(MID_VERIFY, response, &responseSize, verifyTimeout)) {
      if (responseSize >= 36) { // userId(2) + userName(32) + admin(1) + unlockStatus(1)
          result->userId = (response[0] << 8) | response[1];
          memcpy(result->userName, &response[2], MAX_USERNAME_SIZE);
          result->userName[MAX_USERNAME_SIZE-1] = '\0'; // Ensure null termination
          result->admin = response[34];
          result->unlockStatus = response[35];
          
          Serial.print("✓ Verification successful - User ID: ");
          Serial.print(result->userId);
          Serial.print(", Name: ");
          Serial.print(result->userName);
          Serial.print(", Admin: ");
          Serial.print(result->admin);
          Serial.print(", Status: ");
          Serial.println(result->unlockStatus);
          
          return true;
      } else {
          Serial.println("✗ Invalid verify response size");
      }
  } else {
      Serial.println("✗ Verify timeout or failed");
  }
  
  return false;
}

bool FaceScannerEnhanced::enroll(uint8_t admin, const char* userName, uint8_t faceDirection, uint8_t timeout, EnrollResult* result) {
  if (!userName || !result) return false;
  
  uint8_t data[35]; // admin(1) + userName(32) + faceDirection(1) + timeout(1)
  data[0] = admin;
  
  // Copy username with padding
  memset(&data[1], 0, MAX_USERNAME_SIZE);
  strncpy((char*)&data[1], userName, MAX_USERNAME_SIZE-1);
  
  data[33] = faceDirection;
  data[34] = timeout;
  
  Serial.print("→ Starting enrollment - User: ");
  Serial.print(userName);
  Serial.print(", Direction: 0x");
  Serial.print(faceDirection, HEX);
  Serial.print(", Timeout: ");
  Serial.print(timeout);
  Serial.println("s");
  
  if (!sendCommand(MID_ENROLL, data, 35)) {
      Serial.println("✗ Failed to send enroll command");
      return false;
  }
  
  uint8_t response[256];
  uint16_t responseSize;
  uint32_t enrollTimeout = (timeout > 0) ? (timeout * 1000) : 30000;
  
  if (waitForResponse(MID_ENROLL, response, &responseSize, enrollTimeout)) {
      // Debug: Print the actual response data
      Serial.print("📊 Enroll response size: ");
      Serial.print(responseSize);
      Serial.print(" bytes: ");
      for (int i = 0; i < responseSize && i < 8; i++) {
          Serial.print("0x");
          if (response[i] < 16) Serial.print("0");
          Serial.print(response[i], HEX);
          Serial.print(" ");
      }
      Serial.println();

      if (responseSize >= 2) { // Minimum: userId(2) - some responses might be shorter
          result->userId = (response[0] << 8) | response[1];

          // Handle optional face direction
          if (responseSize >= 3) {
              result->faceDirection = response[2];
          } else {
              result->faceDirection = 0; // Default value
          }

          // Handle face data if present
          if (responseSize > 3) {
              result->faceDataSize = responseSize - 3;
              result->faceData = (uint8_t*)malloc(result->faceDataSize);
              if (result->faceData) {
                  memcpy(result->faceData, &response[3], result->faceDataSize);
              }
          } else {
              result->faceData = nullptr;
              result->faceDataSize = 0;
          }

          Serial.print("✓ Enrollment successful - User ID: ");
          Serial.print(result->userId);
          Serial.print(", Direction: 0x");
          Serial.print(result->faceDirection, HEX);
          Serial.print(", Data size: ");
          Serial.println(result->faceDataSize);

          return true;
      } else {
          Serial.print("✗ Invalid enroll response size: ");
          Serial.print(responseSize);
          Serial.println(" bytes (minimum 2 expected)");
      }
  } else {
      Serial.println("✗ Enroll timeout or failed");
  }
  
  return false;
}

bool FaceScannerEnhanced::enrollSingle(uint8_t admin, const char* userName, uint8_t faceDirection, uint8_t timeout, EnrollResult* result) {
  if (!userName || !result) return false;
  
  uint8_t data[35]; // admin(1) + userName(32) + faceDirection(1) + timeout(1)
  data[0] = admin;
  
  // Copy username with padding
  memset(&data[1], 0, MAX_USERNAME_SIZE);
  strncpy((char*)&data[1], userName, MAX_USERNAME_SIZE-1);
  
  data[33] = faceDirection;
  data[34] = timeout;
  
  Serial.print("→ Starting single enrollment - User: ");
  Serial.print(userName);
  Serial.print(", Direction: 0x");
  Serial.print(faceDirection, HEX);
  Serial.println();
  
  if (!sendCommand(MID_ENROLL_SINGLE, data, 35)) {
      Serial.println("✗ Failed to send single enroll command");
      return false;
  }
  
  uint8_t response[256];
  uint16_t responseSize;
  uint32_t enrollTimeout = (timeout > 0) ? (timeout * 1000) : 30000;
  
  if (waitForResponse(MID_ENROLL_SINGLE, response, &responseSize, enrollTimeout)) {
      // Debug: Print the actual response data
      Serial.print("📊 Single enroll response size: ");
      Serial.print(responseSize);
      Serial.print(" bytes: ");
      for (int i = 0; i < responseSize && i < 8; i++) {
          Serial.print("0x");
          if (response[i] < 16) Serial.print("0");
          Serial.print(response[i], HEX);
          Serial.print(" ");
      }
      Serial.println();

      if (responseSize >= 2) { // Minimum: userId(2)
          result->userId = (response[0] << 8) | response[1];

          // Handle optional face direction
          if (responseSize >= 3) {
              result->faceDirection = response[2];
          } else {
              result->faceDirection = 0;
          }

          if (responseSize > 3) {
              result->faceDataSize = responseSize - 3;
              result->faceData = (uint8_t*)malloc(result->faceDataSize);
              if (result->faceData) {
                  memcpy(result->faceData, &response[3], result->faceDataSize);
              }
          } else {
              result->faceData = nullptr;
              result->faceDataSize = 0;
          }

          Serial.print("✓ Single enrollment successful - User ID: ");
          Serial.println(result->userId);
          return true;
      }
  }
  
  Serial.println("✗ Single enroll failed");
  return false;
}

bool FaceScannerEnhanced::deleteUser(uint16_t userId, uint8_t userType) {
  uint8_t data[3];
  data[0] = (userId >> 8) & 0xFF; // user_id_heb
  data[1] = userId & 0xFF;        // user_id_leb
  data[2] = userType;             // user_type
  
  Serial.print("→ Deleting user ID: ");
  Serial.print(userId);
  Serial.print(", Type: ");
  Serial.println(userType);
  
  if (!sendCommand(MID_DELUSER, data, 3)) {
      Serial.println("✗ Failed to send delete user command");
      return false;
  }
  
  if (waitForResponse(MID_DELUSER, nullptr, nullptr, 3000)) {
      Serial.println("✓ User deleted successfully");
      return true;
  }
  
  Serial.println("✗ Delete user failed");
  return false;
}

bool FaceScannerEnhanced::deleteAllUsers(uint8_t type, uint16_t beginUserId, uint16_t endUserId) {
  uint8_t data[5];
  data[0] = type;
  
  if (type == 4) { // Range deletion
      data[1] = (beginUserId >> 8) & 0xFF;
      data[2] = beginUserId & 0xFF;
      data[3] = (endUserId >> 8) & 0xFF;
      data[4] = endUserId & 0xFF;
      
      Serial.print("→ Deleting users in range: ");
      Serial.print(beginUserId);
      Serial.print(" to ");
      Serial.println(endUserId);
      
      if (!sendCommand(MID_DELALL, data, 5)) {
          Serial.println("✗ Failed to send delete all users command");
          return false;
      }
  } else {
      Serial.print("→ Deleting all users, type: ");
      Serial.println(type);
      
      if (!sendCommand(MID_DELALL, data, 1)) {
          Serial.println("✗ Failed to send delete all users command");
          return false;
      }
  }
  
  if (waitForResponse(MID_DELALL, nullptr, nullptr, 5000)) {
      Serial.println("✓ Users deleted successfully");
      return true;
  }
  
  Serial.println("✗ Delete all users failed");
  return false;
}

bool FaceScannerEnhanced::getUserInfo(uint16_t userId, UserInfo* userInfo) {
  if (!userInfo) return false;
  
  uint8_t data[2];
  data[0] = (userId >> 8) & 0xFF; // user_id_heb
  data[1] = userId & 0xFF;        // user_id_leb
  
  Serial.print("→ Getting user info for ID: ");
  Serial.println(userId);
  
  if (!sendCommand(MID_GETUSERINFO, data, 2)) {
      Serial.println("✗ Failed to send get user info command");
      return false;
  }
  
  uint8_t response[36];
  uint16_t responseSize;
  
  if (waitForResponse(MID_GETUSERINFO, response, &responseSize, 3000)) {
      if (responseSize >= 35) { // userId(2) + userName(32) + admin(1)
          userInfo->userId = (response[0] << 8) | response[1];
          memcpy(userInfo->userName, &response[2], MAX_USERNAME_SIZE);
          userInfo->userName[MAX_USERNAME_SIZE-1] = '\0';
          userInfo->admin = response[34];
          
          Serial.print("✓ User info retrieved - ID: ");
          Serial.print(userInfo->userId);
          Serial.print(", Name: ");
          Serial.print(userInfo->userName);
          Serial.print(", Admin: ");
          Serial.println(userInfo->admin);
          
          return true;
      }
  }
  
  Serial.println("✗ Get user info failed");
  return false;
}

bool FaceScannerEnhanced::faceReset() {
  Serial.println("→ Resetting face algorithm state");
  
  if (!sendCommand(MID_FACERESET, nullptr, 0)) {
      Serial.println("✗ Failed to send face reset command");
      return false;
  }
  
  if (waitForResponse(MID_FACERESET, nullptr, nullptr, 1000)) {
      Serial.println("✓ Face reset successful");
      return true;
  }
  
  Serial.println("✗ Face reset failed");
  return false;
}

bool FaceScannerEnhanced::getAllUserIds(uint8_t format, uint16_t* userIds, uint8_t* userCount) {
  if (!userIds || !userCount) return false;
  
  uint8_t data[1] = {format};
  
  Serial.print("→ Getting all user IDs, format: ");
  Serial.println(format);
  
  if (!sendCommand(MID_GET_ALL_USERID, data, 1)) {
      Serial.println("✗ Failed to send get all user IDs command");
      return false;
  }
  
  uint8_t response[512]; // Buffer for multiple user IDs
  uint16_t responseSize;
  
  if (waitForResponse(MID_GET_ALL_USERID, response, &responseSize, 5000)) {
      if (format == 0) { // Count format
          if (responseSize >= 1) {
              *userCount = response[0];
              Serial.print("✓ Total user count: ");
              Serial.println(*userCount);
              return true;
          }
      } else if (format == 1) { // List format
          if (responseSize >= 1) {
              uint8_t count = response[0];
              *userCount = count;
              
              if (responseSize >= (1 + count * 2)) {
                  for (int i = 0; i < count; i++) {
                      userIds[i] = (response[1 + i*2] << 8) | response[2 + i*2];
                  }
                  
                  Serial.print("✓ Retrieved ");
                  Serial.print(count);
                  Serial.println(" user IDs");
                  return true;
              }
          }
      }
  }
  
  Serial.println("✗ Get all user IDs failed");
  return false;
}

bool FaceScannerEnhanced::getVersion(char* version, uint16_t maxLen) {
  if (!version || maxLen == 0) return false;
  
  Serial.println("→ Getting firmware version");
  
  if (!sendCommand(MID_GET_VERSION, nullptr, 0)) {
      Serial.println("✗ Failed to send get version command");
      return false;
  }
  
  uint8_t response[64];
  uint16_t responseSize;
  
  if (waitForResponse(MID_GET_VERSION, response, &responseSize, 10000)) { // Increased timeout to 10s
      if (responseSize > 0) {
          uint16_t copyLen = (responseSize < maxLen-1) ? responseSize : maxLen-1;
          memcpy(version, response, copyLen);
          version[copyLen] = '\0';
          
          Serial.print("✓ Firmware version: ");
          Serial.println(version);
          return true;
      }
  }
  
  Serial.println("✗ Get version failed");
  return false;
}

bool FaceScannerEnhanced::getLibraryVersion(char* version, uint16_t maxLen) {
  if (!version || maxLen == 0) return false;
  
  Serial.println("→ Getting library version");
  
  if (!sendCommand(MID_GETLIBRARY_VERSION, nullptr, 0)) {
      Serial.println("✗ Failed to send get library version command");
      return false;
  }
  
  uint8_t response[64];
  uint16_t responseSize;
  
  if (waitForResponse(MID_GETLIBRARY_VERSION, response, &responseSize, 10000)) { // Increased timeout to 10s
      if (responseSize > 0) {
          uint16_t copyLen = (responseSize < maxLen-1) ? responseSize : maxLen-1;
          memcpy(version, response, copyLen);
          version[copyLen] = '\0';
          
          Serial.print("✓ Library version: ");
          Serial.println(version);
          return true;
      }
  }
  
  Serial.println("✗ Get library version failed");
  return false;
}

bool FaceScannerEnhanced::funcCtrl(uint8_t function, uint8_t* params, uint16_t paramSize) {
  Serial.print("→ Function control: 0x");
  Serial.print(function, HEX);
  Serial.print(", param size: ");
  Serial.println(paramSize);
  
  uint8_t* data = nullptr;
  uint16_t dataSize = 1 + paramSize;
  
  if (paramSize > 0) {
      data = (uint8_t*)malloc(dataSize);
      if (!data) {
          Serial.println("✗ Memory allocation failed");
          return false;
      }
      data[0] = function;
      memcpy(&data[1], params, paramSize);
  } else {
      data = &function;
      dataSize = 1;
  }
  
  bool result = sendCommand(MID_FUNC_CTRL, data, dataSize) &&
                waitForResponse(MID_FUNC_CTRL, nullptr, nullptr, 3000);
  
  if (paramSize > 0 && data) {
      free(data);
  }
  
  if (result) {
      Serial.println("✓ Function control successful");
  } else {
      Serial.println("✗ Function control failed");
  }
  
  return result;
}

bool FaceScannerEnhanced::cameraFlip(uint8_t flipMode) {
  uint8_t data[1] = {flipMode};
  
  Serial.print("→ Setting camera flip mode: ");
  Serial.println(flipMode);
  
  if (!sendCommand(MID_CAMERA_FLIP, data, 1)) {
      Serial.println("✗ Failed to send camera flip command");
      return false;
  }
  
  if (waitForResponse(MID_CAMERA_FLIP, nullptr, nullptr, 1000)) {
      Serial.println("✓ Camera flip mode set successfully");
      return true;
  }
  
  Serial.println("✗ Camera flip failed");
  return false;
}

bool FaceScannerEnhanced::snapImage2(uint8_t imageNumber, uint8_t quality) {
  uint8_t data[2] = {imageNumber, quality};
  
  Serial.print("→ Snapping image2 - Number: ");
  Serial.print(imageNumber);
  Serial.print(", Quality: ");
  Serial.println(quality);
  
  return sendCommand(MID_SNAPIMAGE2, data, 2) &&
         waitForResponse(MID_SNAPIMAGE2, nullptr, nullptr, 10000);
}

// Encryption Methods
bool FaceScannerEnhanced::initEncryption() {
  Serial.println("→ Initializing encryption");
  
  if (!sendCommand(MID_INIT_ENCRYPTION, nullptr, 0)) {
      Serial.println("✗ Failed to send init encryption command");
      return false;
  }
  
  if (waitForResponse(MID_INIT_ENCRYPTION, nullptr, nullptr, 3000)) {
      encryptionEnabled = true;
      Serial.println("✓ Encryption initialized successfully");
      return true;
  }
  
  Serial.println("✗ Init encryption failed");
  return false;
}

bool FaceScannerEnhanced::setReleaseEncKey(uint8_t* key, uint16_t keySize) {
  if (!key || keySize == 0) return false;
  
  Serial.print("→ Setting release encryption key, size: ");
  Serial.println(keySize);
  
  if (!sendCommand(MID_SET_RELEASE_ENC_KEY, key, keySize)) {
      Serial.println("✗ Failed to send set release key command");
      return false;
  }
  
  if (waitForResponse(MID_SET_RELEASE_ENC_KEY, nullptr, nullptr, 3000)) {
      if (keySize <= sizeof(encryptionKey)) {
          memcpy(encryptionKey, key, keySize);
      }
      Serial.println("✓ Release encryption key set successfully");
      return true;
  }
  
  Serial.println("✗ Set release encryption key failed");
  return false;
}

bool FaceScannerEnhanced::setDebugEncKey(uint8_t* key, uint16_t keySize) {
  if (!key || keySize == 0) return false;
  
  Serial.print("→ Setting debug encryption key, size: ");
  Serial.println(keySize);
  
  if (!sendCommand(MID_SET_DEBUG_ENC_KEY, key, keySize)) {
      Serial.println("✗ Failed to send set debug key command");
      return false;
  }
  
  if (waitForResponse(MID_SET_DEBUG_ENC_KEY, nullptr, nullptr, 3000)) {
      if (keySize <= sizeof(encryptionKey)) {
          memcpy(encryptionKey, key, keySize);
      }
      Serial.println("✓ Debug encryption key set successfully");
      return true;
  }
  
  Serial.println("✗ Set debug encryption key failed");
  return false;
}

bool FaceScannerEnhanced::setInteractivate(uint8_t mode) {
  uint8_t data[1] = {mode};
  
  Serial.print("→ Setting interactive mode: ");
  Serial.println(mode);
  
  if (!sendCommand(MID_SET_INTERACTIVATE, data, 1)) {
      Serial.println("✗ Failed to send set interactive command");
      return false;
  }
  
  if (waitForResponse(MID_SET_INTERACTIVATE, nullptr, nullptr, 1000)) {
      Serial.println("✓ Interactive mode set successfully");
      return true;
  }
  
  Serial.println("✗ Set interactive mode failed");
  return false;
}

// OTA Methods
bool FaceScannerEnhanced::startOTA() {
  Serial.println("→ Starting OTA update");
  
  if (!sendCommand(MID_START_OTA, nullptr, 0)) {
      Serial.println("✗ Failed to send start OTA command");
      return false;
  }
  
  if (waitForResponse(MID_START_OTA, nullptr, nullptr, 5000)) {
      Serial.println("✓ OTA started successfully");
      return true;
  }
  
  Serial.println("✗ Start OTA failed");
  return false;
}

bool FaceScannerEnhanced::stopOTA() {
  Serial.println("→ Stopping OTA update");
  
  if (!sendCommand(MID_STOP_OTA, nullptr, 0)) {
      Serial.println("✗ Failed to send stop OTA command");
      return false;
  }
  
  if (waitForResponse(MID_STOP_OTA, nullptr, nullptr, 3000)) {
      Serial.println("✓ OTA stopped successfully");
      return true;
  }
  
  Serial.println("✗ Stop OTA failed");
  return false;
}

bool FaceScannerEnhanced::getOTAStatus(OTAStatus* status) {
  if (!status) return false;
  
  Serial.println("→ Getting OTA status");
  
  if (!sendCommand(MID_GET_OTA_STATUS, nullptr, 0)) {
      Serial.println("✗ Failed to send get OTA status command");
      return false;
  }
  
  uint8_t response[8];
  uint16_t responseSize;
  
  if (waitForResponse(MID_GET_OTA_STATUS, response, &responseSize, 3000)) {
      if (responseSize >= 5) { // status(1) + startPacketNumber(4)
          status->status = response[0];
          status->startPacketNumber = ((uint32_t)response[1] << 24) |
                                     ((uint32_t)response[2] << 16) |
                                     ((uint32_t)response[3] << 8) |
                                     (uint32_t)response[4];
          
          Serial.print("✓ OTA status: ");
          Serial.print(status->status);
          Serial.print(", Start packet: ");
          Serial.println(status->startPacketNumber);
          return true;
      }
  }
  
  Serial.println("✗ Get OTA status failed");
  return false;
}

bool FaceScannerEnhanced::otaHeader(uint8_t* headerData, uint16_t headerSize) {
  if (!headerData || headerSize == 0) return false;
  
  Serial.print("→ Sending OTA header, size: ");
  Serial.println(headerSize);
  
  if (!sendCommand(MID_OTA_HEADER, headerData, headerSize)) {
      Serial.println("✗ Failed to send OTA header command");
      return false;
  }
  
  if (waitForResponse(MID_OTA_HEADER, nullptr, nullptr, 5000)) {
      Serial.println("✓ OTA header sent successfully");
      return true;
  }
  
  Serial.println("✗ OTA header failed");
  return false;
}

bool FaceScannerEnhanced::otaPacket(uint32_t packetNumber, uint8_t* packetData, uint16_t packetSize) {
  if (!packetData || packetSize == 0) return false;
  
  uint8_t* data = (uint8_t*)malloc(4 + packetSize);
  if (!data) {
      Serial.println("✗ Memory allocation failed for OTA packet");
      return false;
  }
  
  // Pack packet number in Big Endian
  data[0] = (packetNumber >> 24) & 0xFF;
  data[1] = (packetNumber >> 16) & 0xFF;
  data[2] = (packetNumber >> 8) & 0xFF;
  data[3] = packetNumber & 0xFF;
  
  memcpy(&data[4], packetData, packetSize);
  
  Serial.print("→ Sending OTA packet ");
  Serial.print(packetNumber);
  Serial.print(", size: ");
  Serial.println(packetSize);
  
  bool result = sendCommand(MID_OTA_PACKET, data, 4 + packetSize) &&
                waitForResponse(MID_OTA_PACKET, nullptr, nullptr, 10000);
  
  free(data);
  
  if (result) {
      Serial.println("✓ OTA packet sent successfully");
  } else {
      Serial.println("✗ OTA packet failed");
  }
  
  return result;
}

bool FaceScannerEnhanced::configBaudrate(uint32_t baudrate) {
  uint8_t data[4];
  data[0] = (baudrate >> 24) & 0xFF;
  data[1] = (baudrate >> 16) & 0xFF;
  data[2] = (baudrate >> 8) & 0xFF;
  data[3] = baudrate & 0xFF;
  
  Serial.print("→ Configuring baudrate: ");
  Serial.println(baudrate);
  
  if (!sendCommand(MID_CONFIG_BAUDRATE, data, 4)) {
      Serial.println("✗ Failed to send config baudrate command");
      return false;
  }
  
  if (waitForResponse(MID_CONFIG_BAUDRATE, nullptr, nullptr, 3000)) {
      Serial.println("✓ Baudrate configured successfully");
      // Note: You may need to reconfigure Serial1 with new baudrate
      return true;
  }
  
  Serial.println("✗ Config baudrate failed");
  return false;
}

// File Transfer Methods
bool FaceScannerEnhanced::getLogFile(uint32_t offset, uint32_t size, uint32_t* actualSize) {
  if (!actualSize) return false;
  
  uint8_t data[8];
  data[0] = (offset >> 24) & 0xFF;
  data[1] = (offset >> 16) & 0xFF;
  data[2] = (offset >> 8) & 0xFF;
  data[3] = offset & 0xFF;
  data[4] = (size >> 24) & 0xFF;
  data[5] = (size >> 16) & 0xFF;
  data[6] = (size >> 8) & 0xFF;
  data[7] = size & 0xFF;
  
  Serial.print("→ Getting log file - Offset: ");
  Serial.print(offset);
  Serial.print(", Size: ");
  Serial.println(size);
  
  if (!sendCommand(MID_GET_LOGFILE, data, 8)) {
      Serial.println("✗ Failed to send get log file command");
      return false;
  }
  
  if (waitForResponse(MID_GET_LOGFILE, nullptr, nullptr, 10000)) {
      *actualSize = size; // This would need to be parsed from response
      Serial.println("✓ Log file retrieved successfully");
      return true;
  }
  
  Serial.println("✗ Get log file failed");
  return false;
}

bool FaceScannerEnhanced::uploadLogFile(uint32_t offset, uint32_t size, uint8_t* logData, uint16_t* actualSize) {
  if (!logData || !actualSize) return false;
  
  uint8_t data[8];
  data[0] = (offset >> 24) & 0xFF;
  data[1] = (offset >> 16) & 0xFF;
  data[2] = (offset >> 8) & 0xFF;
  data[3] = offset & 0xFF;
  data[4] = (size >> 24) & 0xFF;
  data[5] = (size >> 16) & 0xFF;
  data[6] = (size >> 8) & 0xFF;
  data[7] = size & 0xFF;
  
  Serial.print("→ Uploading log file - Offset: ");
  Serial.print(offset);
  Serial.print(", Size: ");
  Serial.println(size);
  
  if (!sendCommand(MID_UPLOAD_LOGFILE, data, 8)) {
      Serial.println("✗ Failed to send upload log file command");
      return false;
  }
  
  if (waitForStreamResponse(MID_UPLOAD_LOGFILE, logData, size, 15000)) {
      *actualSize = size;
      Serial.println("✓ Log file uploaded successfully");
      return true;
  }
  
  Serial.println("✗ Upload log file failed");
  return false;
}

bool FaceScannerEnhanced::transFilePacket(uint8_t* fileData, uint16_t fileSize) {
  if (!fileData || fileSize == 0) return false;
  
  Serial.print("→ Transferring file packet, size: ");
  Serial.println(fileSize);
  
  if (!sendCommand(MID_TRANS_FILE_PACKET, fileData, fileSize)) {
      Serial.println("✗ Failed to send transfer file packet command");
      return false;
  }
  
  if (waitForResponse(MID_TRANS_FILE_PACKET, nullptr, nullptr, 10000)) {
      Serial.println("✓ File packet transferred successfully");
      return true;
  }
  
  Serial.println("✗ Transfer file packet failed");
  return false;
}

// Note handling methods
bool FaceScannerEnhanced::waitForNote(uint8_t expectedNoteId, uint8_t* noteData, uint16_t* noteSize, uint32_t timeoutMs) {
  uint32_t startTime = millis();
  
  while (millis() - startTime < timeoutMs) {
      if (Serial1.available() >= 6) { // Minimum packet size
          uint8_t header[6];
          Serial1.readBytes(header, 6);
          
          // Check sync word and message ID
          if (header[0] == SYNC_WORD_0 && header[1] == SYNC_WORD_1 && header[2] == MID_NOTE) {
              uint16_t dataSize = (header[3] << 8) | header[4];
              
              if (dataSize > 0 && Serial1.available() >= dataSize + 1) { // +1 for checksum
                  uint8_t* buffer = (uint8_t*)malloc(dataSize + 1);
                  if (buffer) {
                      Serial1.readBytes(buffer, dataSize + 1);
                      
                      // Verify checksum
                      uint8_t checksum = header[2] ^ header[3] ^ header[4];
                      for (int i = 0; i < dataSize; i++) {
                          checksum ^= buffer[i];
                      }
                      
                      if (checksum == buffer[dataSize]) {
                          uint8_t noteId = buffer[0];
                          if (noteId == expectedNoteId || expectedNoteId == 0xFF) { // 0xFF = any note
                              if (noteData && noteSize) {
                                  *noteSize = dataSize - 1;
                                  memcpy(noteData, &buffer[1], *noteSize);
                              }
                              free(buffer);
                              return true;
                          }
                      }
                      free(buffer);
                  }
              }
          }
      }
      delay(10);
  }
  
  return false;
}

bool FaceScannerEnhanced::parseFaceInfo(uint8_t* noteData, uint16_t noteSize, FaceInfo* faceInfo) {
  if (!noteData || !faceInfo || noteSize < 16) return false;
  
  // Parse s_note_data_face structure (16 bytes total)
  faceInfo->state = (noteData[0] << 8) | noteData[1];
  faceInfo->left = (noteData[2] << 8) | noteData[3];
  faceInfo->top = (noteData[4] << 8) | noteData[5];
  faceInfo->right = (noteData[6] << 8) | noteData[7];
  faceInfo->bottom = (noteData[8] << 8) | noteData[9];
  faceInfo->yaw = (noteData[10] << 8) | noteData[11];
  faceInfo->pitch = (noteData[12] << 8) | noteData[13];
  faceInfo->roll = (noteData[14] << 8) | noteData[15];
  
  Serial.print("Face Info - State: ");
  Serial.print(faceInfo->state);
  Serial.print(", Position: (");
  Serial.print(faceInfo->left);
  Serial.print(",");
  Serial.print(faceInfo->top);
  Serial.print(",");
  Serial.print(faceInfo->right);
  Serial.print(",");
  Serial.print(faceInfo->bottom);
  Serial.print("), Angles: (");
  Serial.print(faceInfo->yaw);
  Serial.print(",");
  Serial.print(faceInfo->pitch);
  Serial.print(",");
  Serial.print(faceInfo->roll);
  Serial.println(")");
  
  return true;
}

// Private helper methods for encryption
void FaceScannerEnhanced::encryptData(uint8_t* data, uint16_t size) {
  if (!encryptionEnabled || !data || size == 0) return;
  
  // Simple XOR encryption with key (implement proper encryption as needed)
  for (uint16_t i = 0; i < size; i++) {
      data[i] ^= encryptionKey[i % sizeof(encryptionKey)];
  }
}

void FaceScannerEnhanced::decryptData(uint8_t* data, uint16_t size) {
  if (!encryptionEnabled || !data || size == 0) return;
  
  // Simple XOR decryption with key (implement proper decryption as needed)
  for (uint16_t i = 0; i < size; i++) {
      data[i] ^= encryptionKey[i % sizeof(encryptionKey)];
  }
}

bool FaceScannerEnhanced::isEncryptionRequired(uint8_t msgId) {
  // Define which commands require encryption
  switch (msgId) {
      case MID_ENROLL:
      case MID_ENROLL_SINGLE:
      case MID_VERIFY:
      case MID_GETUSERINFO:
          return encryptionEnabled;
      default:
          return false;
  }
}

// Existing private methods (sendCommand, waitForResponse, waitForStreamResponse)
// These would remain exactly as they are in the original FaceScanner.cpp
bool FaceScannerEnhanced::sendCommand(uint8_t msgId, uint8_t* data, uint16_t dataSize) {
    if (dataSize > 200) {
        Serial.println("✗ Data size too large");
        return false;
    }
    
    uint8_t packet[256];
    uint16_t packetSize = 5 + dataSize;
    
    if (packetSize > sizeof(packet)) {
        Serial.println("✗ Packet size exceeds buffer");
        return false;
    }
    
    // Build packet with Big Endian length field
    packet[0] = SYNC_WORD[0];
    packet[1] = SYNC_WORD[1];
    packet[2] = msgId;
    packet[3] = (dataSize >> 8) & 0xFF;
    packet[4] = dataSize & 0xFF;
    
    if (data != nullptr && dataSize > 0) {
        if (dataSize <= sizeof(packet) - 5) {
            memcpy(&packet[5], data, dataSize);
        } else {
            Serial.println("✗ Data copy would exceed buffer");
            return false;
        }
    }
    
    // Calculate XOR checksum (from byte 2 to end of data)
    uint8_t checksum = 0;
    for (uint16_t i = 2; i < packetSize; i++) {
        checksum ^= packet[i];
    }
    packet[packetSize] = checksum;
    
    Serial.print("→ Sending command 0x");
    Serial.print(msgId, HEX);
    Serial.print(" (");
    Serial.print(dataSize);
    Serial.println(" bytes data)");
    
    clearSerialBuffer();
    Serial1.write(packet, packetSize + 1);
    Serial1.flush();
    
    return true;
}

bool FaceScannerEnhanced::waitForResponse(uint8_t expectedMsgId, uint8_t* response, uint16_t* responseSize, uint32_t timeoutMs) {
    if (!responseBuffer) {
        Serial.println("✗ Response buffer not allocated");
        return false;
    }
    
    uint32_t startTime = millis();
    uint8_t state = 0;
    uint8_t msgId = 0;
    uint16_t dataSize = 0;
    uint16_t bytesRead = 0;
    
    memset(responseBuffer, 0, MAX_RESPONSE_SIZE);
    
    while (millis() - startTime < timeoutMs) {
        if (Serial1.available()) {
            uint8_t byte = Serial1.read();
            
            switch (state) {
                case 0:
                    if (byte == SYNC_WORD[0]) {
                        state = 1;
                    }
                    break;
                    
                case 1:
                    if (byte == SYNC_WORD[1]) {
                        state = 2;
                        bytesRead = 0;
                    } else {
                        state = 0;
                    }
                    break;
                    
                case 2:
                    if (bytesRead == 0) {
                        msgId = byte;
                        bytesRead++;
                    } else if (bytesRead == 1) {
                        dataSize = byte << 8;
                        bytesRead++;
                    } else if (bytesRead == 2) {
                        dataSize |= byte;
                        bytesRead = 0;
                        
                        Serial.print("← Response: MsgID=0x");
                        Serial.print(msgId, HEX);
                        Serial.print(", DataSize=");
                        Serial.println(dataSize);
                        
                        if (dataSize > MAX_RESPONSE_SIZE - 10) {
                            Serial.println("✗ Response data too large");
                            return false;
                        }
                        
                        if (msgId == MID_REPLY) {
                            state = 3; // Handle reply messages
                        } else if (msgId == MID_NOTE) {
                            state = 4; // Handle notification messages
                        } else if (msgId == MID_IMAGE) {
                            state = 5; // Handle image messages
                        } else {
                            Serial.print("✗ Unexpected message ID: 0x");
                            Serial.println(msgId, HEX);
                            state = 0;
                        }
                    }
                    break;
                    
                case 3:
                    if (bytesRead == 0) {
                        uint8_t replyMsgId = byte;
                        if (replyMsgId == expectedMsgId) {
                            bytesRead++;
                        } else {
                            Serial.print("✗ Wrong reply ID: expected 0x");
                            Serial.print(expectedMsgId, HEX);
                            Serial.print(", got 0x");
                            Serial.println(replyMsgId, HEX);
                            state = 0;
                        }
                    } else if (bytesRead == 1) {
                        uint8_t result = byte;
                        bytesRead++;
                        dataSize -= 2;
                        
                        if (result != MR_SUCCESS) {
                            Serial.print("✗ Command failed with result: 0x");
                            Serial.print(result, HEX);
                            Serial.print(" (");
                            Serial.print(getErrorDescription(result));
                            Serial.println(")");
                            return false;
                        }
                        
                        Serial.println("✓ Command successful");
                        
                        if (dataSize == 0) {
                            if (responseSize) *responseSize = 0;
                            return true;
                        }
                    } else {
                        uint16_t bufferIndex = bytesRead - 2;
                        if (bufferIndex < MAX_RESPONSE_SIZE && bufferIndex < dataSize) {
                            responseBuffer[bufferIndex] = byte;
                        }
                        bytesRead++;
                        
                        if (bytesRead >= dataSize + 2) {
                            if (response && responseSize && dataSize <= MAX_RESPONSE_SIZE) {
                                memcpy(response, responseBuffer, dataSize);
                                *responseSize = dataSize;
                            }
                            return true;
                        }
                    }
                    break;

                case 4: // Handle notification messages (MID_NOTE)
                    if (bytesRead < dataSize) {
                        responseBuffer[bytesRead] = byte;
                        bytesRead++;

                        if (bytesRead >= dataSize) {
                            // Process notification
                            if (dataSize >= 1) {
                                uint8_t noteId = responseBuffer[0];
                                handleNotification(noteId, &responseBuffer[1], dataSize - 1);
                            }
                            // Reset to wait for next message (could be the actual response)
                            state = 0;
                            bytesRead = 0;
                        }
                    }
                    break;

                case 5: // Handle image messages (MID_IMAGE)
                    if (bytesRead < dataSize) {
                        responseBuffer[bytesRead] = byte;
                        bytesRead++;

                        if (bytesRead >= dataSize) {
                            // Process image data
                            handleImageData(responseBuffer, dataSize);
                            // Reset to wait for next message
                            state = 0;
                            bytesRead = 0;
                        }
                    }
                    break;
            }
        }
        yield();
    }

    Serial.println("✗ Response timeout");
    return false;
}

void FaceScannerEnhanced::handleNotification(uint8_t noteId, uint8_t* data, uint16_t dataSize) {
    switch (noteId) {
        case NID_READY:
            Serial.println("📢 Module ready");
            break;

        case NID_FACE_STATE:
            if (dataSize >= 16) { // FaceInfo structure
                FaceInfo faceInfo;
                faceInfo.state = (data[0] << 8) | data[1];
                faceInfo.left = (data[2] << 8) | data[3];
                faceInfo.top = (data[4] << 8) | data[5];
                faceInfo.right = (data[6] << 8) | data[7];
                faceInfo.bottom = (data[8] << 8) | data[9];
                faceInfo.yaw = (data[10] << 8) | data[11];
                faceInfo.pitch = (data[12] << 8) | data[13];
                faceInfo.roll = (data[14] << 8) | data[15];

                Serial.print("📢 Face state: 0x");
                Serial.print(faceInfo.state, HEX);
                Serial.print(" at (");
                Serial.print(faceInfo.left);
                Serial.print(",");
                Serial.print(faceInfo.top);
                Serial.print(",");
                Serial.print(faceInfo.right);
                Serial.print(",");
                Serial.print(faceInfo.bottom);
                Serial.println(")");
            }
            break;

        case NID_UNKNOWNERROR:
            Serial.println("📢 Unknown error notification");
            break;

        case NID_OTA_DONE:
            Serial.println("📢 OTA update completed");
            break;

        case NID_EYE_STATE:
            Serial.println("📢 Eye state notification");
            break;

        default:
            Serial.print("📢 Unknown notification ID: 0x");
            Serial.println(noteId, HEX);
            break;
    }
}

void FaceScannerEnhanced::handleImageData(uint8_t* data, uint16_t dataSize) {
    Serial.print("📢 Image data received: ");
    Serial.print(dataSize);
    Serial.println(" bytes");
    // Image data handling can be implemented based on specific needs
}

const char* FaceScannerEnhanced::getErrorDescription(uint8_t errorCode) {
    switch (errorCode) {
        case MR_SUCCESS: return "Success";
        case MR_REJECTED: return "Rejected";
        case MR_ABORTED: return "Aborted";
        case MR_FAILED4_CAMERA: return "Camera failure";
        case MR_FAILED4_UNKNOWNREASON: return "Unknown reason";
        case MR_FAILED4_INVALIDPARAM: return "Invalid parameter";
        case MR_FAILED4_NOMEMORY: return "No memory";
        case MR_FAILED4_UNKNOWNUSER: return "Unknown user";
        case MR_FAILED4_MAXUSER: return "Maximum users reached";
        case MR_FAILED4_FACEENROLLED: return "Face already enrolled";
        case MR_FAILED4_LIVENESSCHECK: return "Liveness check failed";
        case MR_FAILED4_TIMEOUT: return "Timeout";
        case MR_FAILED4_AUTHORIZATION: return "Authorization failed";
        case MR_FAILED4_READ_FILE: return "Read file failed";
        case MR_FAILED4_WRITE_FILE: return "Write file failed";
        case MR_FAILED4_NO_ENCRYPT: return "No encryption";
        case MR_FAILED4_NO_RGBIMAGE: return "No RGB image";
        case MR_FAILED4_UNKNOWN_HANDUSER: return "Unknown hand user";
        case MR_FAILED4_NOCAMERA: return "No camera";
        case MR_FAILED4_HANDENROLLED: return "Hand already enrolled";
        default: return "Unknown error";
    }
}

bool FaceScannerEnhanced::waitForStreamResponse(uint8_t expectedMsgId, uint8_t* buffer, uint32_t bufferSize, uint32_t timeoutMs) {
  uint32_t startTime = millis();
  uint32_t totalReceived = 0;
  uint32_t lastChunkTime = millis();
  bool isEncrypted = isEncryptionRequired(expectedMsgId);
  
  while (millis() - startTime < timeoutMs && totalReceived < bufferSize) {
      // Check for inter-chunk timeout (500ms max between chunks)
      if (millis() - lastChunkTime > 500) {
          Serial.println("✗ Inter-chunk timeout");
          break;
      }
      
      if (Serial1.available() >= 6) {
          uint8_t header[6];
          Serial1.readBytes(header, 6);
          
          // Validate sync word and message ID
          if (header[0] == SYNC_WORD_0 && header[1] == SYNC_WORD_1 && 
              (header[2] == expectedMsgId || header[2] == MID_IMAGE || header[2] == MID_NOTE)) {
              
              uint16_t dataSize = (header[3] << 8) | header[4];
              
              // Validate data size
              if (dataSize == 0 || dataSize > MAX_CHUNK_SIZE) {
                  Serial.println("✗ Invalid data size in stream");
                  break;
              }
              
              if (Serial1.available() >= dataSize + 1) {
                  // Check buffer bounds
                  if (totalReceived + dataSize > bufferSize) {
                      Serial.println("✗ Stream would overflow buffer");
                      break;
                  }
                  
                  // Read data chunk
                  uint8_t* chunkStart = &buffer[totalReceived];
                  Serial1.readBytes(chunkStart, dataSize);
                  totalReceived += dataSize;
                  
                  // Read and verify checksum
                  uint8_t checksum;
                  Serial1.readBytes(&checksum, 1);
                  
                  // Calculate expected checksum
                  uint8_t expectedChecksum = header[2] ^ header[3] ^ header[4];
                  for (uint16_t i = 0; i < dataSize; i++) {
                      expectedChecksum ^= chunkStart[i];
                  }
                  
                  if (checksum != expectedChecksum) {
                      Serial.println("✗ Stream checksum mismatch");
                      break;
                  }
                  
                  // Decrypt if needed
                  if (isEncrypted) {
                      decryptData(chunkStart, dataSize);
                  }
                  
                  lastChunkTime = millis();
                  Serial.print("← Stream chunk: ");
                  Serial.print(dataSize);
                  Serial.print(" bytes, total: ");
                  Serial.println(totalReceived);
                  
                  // Check for completion
                  if (totalReceived >= bufferSize) {
                      Serial.println("✓ Stream complete");
                      return true;
                  }
              }
          } else {
              Serial.println("✗ Invalid stream header");
              break;
          }
      }
      delay(5);
  }
  
  if (totalReceived > 0) {
      Serial.print("✓ Partial stream received: ");
      Serial.print(totalReceived);
      Serial.println(" bytes");
      return true;
  }
  
  Serial.println("✗ Stream timeout");
  return false;
}
