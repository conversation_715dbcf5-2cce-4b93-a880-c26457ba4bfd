#ifndef FACESCANNER_ENHANCED_H
#define FACESCANNER_ENHANCED_H

#include <Arduino.h>

// Pin definitions for UART communication
#define RXD1 34
#define TXD1 33

// Protocol constants
#define SYNC_WORD_0 0xEF
#define SYNC_WORD_1 0xAA
static const uint8_t SYNC_WORD[2] = {SYNC_WORD_0, SYNC_WORD_1};

// Message IDs - Existing
#define MID_RESET 0x10
#define MID_GETSTATUS 0x11
#define MID_SNAPIMAGE 0x16
#define MID_GETSAVEDIMAGE 0x17
#define MID_UPLOADIMAGE 0x18
#define MID_REPLY 0x00

// Message IDs - New from PDF
#define MID_VERIFY 0x12
#define MID_ENROLL 0x13
#define MID_ENROLL_SINGLE 0x1D
#define MID_DELUSER 0x20
#define MID_DELALL 0x21
#define MID_GETUSERINFO 0x22
#define MID_FACERESET 0x23
#define MID_GET_ALL_USERID 0x24
#define MID_ENROLL_ITG 0x26
#define MID_GET_VERSION 0x30
#define MID_START_OTA 0x40
#define MID_STOP_OTA 0x41
#define MID_GET_OTA_STATUS 0x42
#define MID_OTA_HEADER 0x43
#define MID_OTA_PACKET 0x44
#define MID_INIT_ENCRYPTION 0x50
#define MID_CONFIG_BAUDRATE 0x51
#define MID_SET_RELEASE_ENC_KEY 0x52
#define MID_SET_DEBUG_ENC_KEY 0x53
#define MID_GET_LOGFILE 0x60
#define MID_UPLOAD_LOGFILE 0x61
#define MID_SNAPIMAGE2 0x70
#define MID_FUNC_CTRL 0x80
#define MID_CAMERA_FLIP 0x81
#define MID_TRANS_FILE_PACKET 0x90
#define MID_SET_INTERACTIVATE 0xA0
#define MID_GETLIBRARY_VERSION 0xF3

// Note IDs
#define MID_NOTE 0x01
#define MID_IMAGE 0x02
#define NID_READY 0x00
#define NID_FACE_STATE 0x01
#define NID_UNKNOWNERROR 0x02
#define NID_OTA_DONE 0x03
#define NID_EYE_STATE 0x04

// Result codes
#define MR_SUCCESS 0x00
#define MR_REJECTED 0x01
#define MR_ABORTED 0x02
#define MR_FAILED4_CAMERA 0x04
#define MR_FAILED4_UNKNOWNREASON 0x05
#define MR_FAILED4_INVALIDPARAM 0x06
#define MR_FAILED4_NOMEMORY 0x07
#define MR_FAILED4_UNKNOWNUSER 0x08
#define MR_FAILED4_MAXUSER 0x09
#define MR_FAILED4_FACEENROLLED 0x0A
#define MR_FAILED4_LIVENESSCHECK 0x0C
#define MR_FAILED4_TIMEOUT 0x0D
#define MR_FAILED4_AUTHORIZATION 0x0E
#define MR_FAILED4_READ_FILE 0x13
#define MR_FAILED4_WRITE_FILE 0x14
#define MR_FAILED4_NO_ENCRYPT 0x15
#define MR_FAILED4_NO_RGBIMAGE 0x17
#define MR_FAILED4_UNKNOWN_HANDUSER 0xEF
#define MR_FAILED4_NOCAMERA 0xF0
#define MR_FAILED4_HANDENROLLED 0xF1

// Face directions
#define FACE_DIRECTION_FRONT 0x01
#define FACE_DIRECTION_UP 0x02
#define FACE_DIRECTION_DOWN 0x03
#define FACE_DIRECTION_LEFT 0x04
#define FACE_DIRECTION_RIGHT 0x05
#define FACE_DIRECTION_HAND 0xFB
#define FACE_DIRECTION_PICTURE 0xFD
#define FACE_DIRECTION_RENT 0xFC
#define FACE_DIRECTION_FACE_ONLY 0xFE

// Face states
#define FACE_STATE_NORMAL 0x00
#define FACE_STATE_NOFACE 0x01
#define FACE_STATE_TOOUP 0x02
#define FACE_STATE_TOODOWN 0x03
#define FACE_STATE_TOOLEFT 0x04
#define FACE_STATE_TOORIGHT 0x05
#define FACE_STATE_FAR 0x06
#define FACE_STATE_CLOSE 0x07
#define FACE_STATE_EYEBROW_OCCLUSION 0x08
#define FACE_STATE_EYE_OCCLUSION 0x09
#define FACE_STATE_FACE_OCCLUSION 0x0A
#define FACE_STATE_DIRECTION_ERROR 0x0B
#define FACE_STATE_EYE_CLOSE_STATUS_OPEN_EYE 0x0C
#define FACE_STATE_EYE_CLOSE_STATUS 0x0D
#define FACE_STATE_EYE_CLOSE_UNKNOW_STATUS 0x0E
#define FACE_STATE_HAND_NORMAL 0x80
#define FACE_STATE_HAND_FAR 0x81
#define FACE_STATE_HAND_CLOSE 0x82
#define FACE_STATE_HAND_TOOUP 0x83
#define FACE_STATE_HAND_TOODOWN 0x84
#define FACE_STATE_HAND_TOOLEFT 0x85
#define FACE_STATE_HAND_TOORIGHT 0x86

// Module status
#define MS_STANDBY 0x00
#define MS_BUSY 0x01
#define MS_ERROR 0x02
#define MS_INVALID 0x03
#define MS_OTA 0x04

// Verify modes
#define VERIFY_MODE_STANDARD 0x00
#define VERIFY_MODE_PALM 0x01
#define VERIFY_MODE_IRIS 0x02
#define VERIFY_MODE_QRCODE 0x04
#define VERIFY_MODE_FACE_PALM 0x08

// Buffer sizes
#define MAX_RESPONSE_SIZE 1024
#define STREAM_BUFFER_SIZE 2048
#define MAX_CHUNK_SIZE 4000
#define MAX_USERNAME_SIZE 32
#define MAX_USERS 1000

// Data structures
struct FaceInfo {
  int16_t state;
  int16_t left;
  int16_t top;
  int16_t right;
  int16_t bottom;
  int16_t yaw;
  int16_t pitch;
  int16_t roll;
};

struct UserInfo {
  uint16_t userId;
  char userName[MAX_USERNAME_SIZE];
  uint8_t admin;
  uint8_t userType;
};

struct VerifyResult {
  uint16_t userId;
  char userName[MAX_USERNAME_SIZE];
  uint8_t admin;
  uint8_t unlockStatus;
};

struct EnrollResult {
  uint16_t userId;
  uint8_t faceDirection;
  uint8_t* faceData;
  uint16_t faceDataSize;
};

struct OTAStatus {
  uint8_t status;
  uint32_t startPacketNumber;
};

class FaceScannerEnhanced {
private:
  uint8_t* responseBuffer;
  uint8_t* streamBuffer;
  bool useStreamBuffer;
  bool encryptionEnabled;
  uint8_t encryptionKey[16];
  
  // Existing private methods
  void clearSerialBuffer();
  bool sendCommand(uint8_t msgId, uint8_t* data, uint16_t dataSize);
  bool waitForResponse(uint8_t expectedMsgId, uint8_t* response, uint16_t* responseSize, uint32_t timeoutMs);
  bool waitForStreamResponse(uint8_t expectedMsgId, uint8_t* buffer, uint32_t bufferSize, uint32_t timeoutMs);
  
  // New private methods for encryption
  void encryptData(uint8_t* data, uint16_t size);
  void decryptData(uint8_t* data, uint16_t size);
  bool isEncryptionRequired(uint8_t msgId);

public:
  FaceScannerEnhanced();
  ~FaceScannerEnhanced();
  
  // Existing public methods
  bool begin();
  bool reset();
  bool getStatus(uint8_t* status);
  bool snapImage(uint8_t imageNumber, uint8_t quality);
  bool getSavedImageSize(uint8_t imageNumber, uint32_t* imageSize);
  bool uploadImage(uint32_t offset, uint32_t size, uint8_t* imageData, uint16_t* actualSize);
  
  // New public methods - User Management
  bool verify(uint8_t pdRightaway, uint8_t timeout, uint8_t verifyMode, VerifyResult* result);
  bool enroll(uint8_t admin, const char* userName, uint8_t faceDirection, uint8_t timeout, EnrollResult* result);
  bool enrollSingle(uint8_t admin, const char* userName, uint8_t faceDirection, uint8_t timeout, EnrollResult* result);
  bool deleteUser(uint16_t userId, uint8_t userType);
  bool deleteAllUsers(uint8_t type, uint16_t beginUserId = 0, uint16_t endUserId = 0);
  bool getUserInfo(uint16_t userId, UserInfo* userInfo);
  bool faceReset();
  bool getAllUserIds(uint8_t format, uint16_t* userIds, uint8_t* userCount);
  
  // New public methods - System Control
  bool getVersion(char* version, uint16_t maxLen);
  bool getLibraryVersion(char* version, uint16_t maxLen);
  bool funcCtrl(uint8_t function, uint8_t* params, uint16_t paramSize);
  bool cameraFlip(uint8_t flipMode);
  bool snapImage2(uint8_t imageNumber, uint8_t quality);
  
  // New public methods - Encryption
  bool initEncryption();
  bool setReleaseEncKey(uint8_t* key, uint16_t keySize);
  bool setDebugEncKey(uint8_t* key, uint16_t keySize);
  bool setInteractivate(uint8_t mode);
  
  // New public methods - OTA
  bool startOTA();
  bool stopOTA();
  bool getOTAStatus(OTAStatus* status);
  bool otaHeader(uint8_t* headerData, uint16_t headerSize);
  bool otaPacket(uint32_t packetNumber, uint8_t* packetData, uint16_t packetSize);
  bool configBaudrate(uint32_t baudrate);
  
  // New public methods - File Transfer
  bool getLogFile(uint32_t offset, uint32_t size, uint32_t* actualSize);
  bool uploadLogFile(uint32_t offset, uint32_t size, uint8_t* logData, uint16_t* actualSize);
  bool transFilePacket(uint8_t* fileData, uint16_t fileSize);
  
  // New public methods - Note handling
  bool waitForNote(uint8_t expectedNoteId, uint8_t* noteData, uint16_t* noteSize, uint32_t timeoutMs);
  bool parseFaceInfo(uint8_t* noteData, uint16_t noteSize, FaceInfo* faceInfo);
};


#endif // FACESCANNER_ENHANCED_H
