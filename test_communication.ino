/*
 * Face Scanner Communication Test
 * 
 * This is a simplified test to verify the communication protocol fixes.
 * Upload this to test basic communication without enrollment/verification.
 */

#include "FaceScannerEnhanced.h"

FaceScannerEnhanced faceScanner;

void setup() {
  Serial.begin(115200);
  delay(2000);
  
  Serial.println("=== Face Scanner Communication Test ===");
  
  // Initialize the face scanner
  if (faceScanner.begin()) {
    Serial.println("✓ Face scanner initialized successfully");
  } else {
    Serial.println("✗ Failed to initialize face scanner");
    return;
  }
  
  // Test basic commands
  testBasicCommands();
  
  Serial.println("=== Test Complete ===");
}

void loop() {
  // Test periodic status check
  static unsigned long lastCheck = 0;
  if (millis() - lastCheck > 10000) { // Every 10 seconds
    lastCheck = millis();
    
    uint8_t status;
    if (faceScanner.getStatus(&status)) {
      Serial.print("📊 Periodic status check - Module status: ");
      Serial.println(status);
    }
  }
  
  delay(100);
}

void testBasicCommands() {
  Serial.println("\n--- Testing Basic Commands ---");
  
  // Test 1: Get firmware version
  Serial.println("\n1. Testing firmware version...");
  char version[64];
  if (faceScanner.getVersion(version, sizeof(version))) {
    Serial.print("✓ Firmware version: ");
    Serial.println(version);
  } else {
    Serial.println("✗ Failed to get firmware version");
  }
  
  // Test 2: Get library version (may fail on some firmware)
  Serial.println("\n2. Testing library version...");
  if (faceScanner.getLibraryVersion(version, sizeof(version))) {
    Serial.print("✓ Library version: ");
    Serial.println(version);
  } else {
    Serial.println("ℹ Library version command not supported (this is normal)");
  }
  
  // Test 3: Get module status
  Serial.println("\n3. Testing module status...");
  uint8_t status;
  if (faceScanner.getStatus(&status)) {
    Serial.print("✓ Module status: ");
    Serial.print(status);
    Serial.print(" (");
    switch (status) {
      case 0: Serial.print("Standby"); break;
      case 1: Serial.print("Busy"); break;
      case 2: Serial.print("Error"); break;
      case 3: Serial.print("Invalid"); break;
      case 4: Serial.print("OTA"); break;
      default: Serial.print("Unknown"); break;
    }
    Serial.println(")");
  } else {
    Serial.println("✗ Failed to get module status");
  }
  
  // Test 4: Reset module
  Serial.println("\n4. Testing module reset...");
  if (faceScanner.reset()) {
    Serial.println("✓ Module reset successful");
  } else {
    Serial.println("✗ Module reset failed");
  }
  
  // Test 5: Get user count
  Serial.println("\n5. Testing user count...");
  uint8_t userCount;
  if (faceScanner.getAllUserIds(0, &userCount, nullptr, 0)) {
    Serial.print("✓ Total users enrolled: ");
    Serial.println(userCount);
  } else {
    Serial.println("✗ Failed to get user count");
  }
  
  Serial.println("\n--- Basic Commands Test Complete ---");
}
