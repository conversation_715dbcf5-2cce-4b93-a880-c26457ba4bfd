# Face Scanner Testing Guide

## Quick Test Procedure

### 1. Upload the Code
1. Open Arduino IDE
2. Open `FaceScannerExample.ino`
3. Select your ESP32 board and port
4. Click Upload

### 2. Monitor Serial Output
1. Open Serial Monitor (Tools → Serial Monitor)
2. Set baud rate to **115200**
3. Reset the ESP32 or power cycle it
4. Observe the output

## Expected Output (Fixed Version)

```
=== Face Scanner Enhanced Example ===
✓ Communication established at 115200 baud
✓ Command successful
✓ Firmware version: 3.231.1_D3
→ Getting library version
✗ Command failed with result: 0x1 (Rejected)
ℹ Library version command not supported by this firmware
✓ Command successful
Module status: 0
→ Clearing existing users
✓ Command successful
→ Starting user enrollment
→ Sending command 0x13 (35 bytes data)
📢 Face state: 0x... (face detection notifications - NORMAL)
📢 Face state: 0x... (more notifications - NORMAL)
← Response: MsgID=0x0, DataSize=5
✗ Command failed with result: 0xD (Timeout)
✗ User enrollment failed - this is normal if no face is detected
→ Starting user verification
→ Sending command 0x12 (3 bytes data)
📢 Face state: 0x... (face detection notifications - NORMAL)
← Response: MsgID=0x0, DataSize=38
✓ Command successful
✓ Verification successful - User ID: 1, Name: Test User, Admin: 0, Status: 200
User verified: Test User (ID: 1)
```

## Key Improvements ✅

### Before (Broken):
```
✗ Unexpected message ID
✗ Unexpected message ID
✗ Unexpected message ID
✗ Command failed with result: 0xA
✗ Enroll timeout or failed
```

### After (Fixed):
```
📢 Face state: 0x... (notifications properly handled)
✗ Command failed with result: 0xD (Timeout) ← Clear error description
✗ User enrollment failed - this is normal if no face is detected
```

## What Each Message Means

### ✅ **Normal/Expected Messages:**
- `✓ Communication established` - Face scanner connected successfully
- `✓ Firmware version: X.X.X` - Firmware version retrieved
- `ℹ Library version command not supported` - Normal for some firmware versions
- `📢 Face state: 0x...` - Face detection notifications (NEW - these were causing errors before)
- `✗ Command failed with result: 0xD (Timeout)` - Normal if no face is presented during enrollment
- `✗ User enrollment failed - this is normal if no face is detected` - Expected behavior

### ❌ **Error Messages (if they appear):**
- `✗ Failed to initialize face scanner` - Check wiring and power
- `✗ Communication timeout` - Check baud rate and connections
- `✗ Response timeout` - Module may not be responding

## Testing Different Scenarios

### Test 1: Basic Communication (Should Always Work)
- Firmware version should be displayed
- Module status should show (usually 0 = Standby)
- No "Unexpected message ID" errors

### Test 2: Enrollment (Should work when face is detected)
- Should clear existing users first
- Will show face detection notifications with coordinates
- Should succeed if face is properly positioned
- Will show debug info about response bytes received

### Test 3: Verification (Depends on enrolled users)
- Will show face detection notifications
- May succeed if users are already enrolled
- Will fail gracefully if no users enrolled

## Troubleshooting

### If you still see "Unexpected message ID":
1. Make sure you uploaded the **fixed** version of the code
2. Check that only `FaceScannerExample.ino` is in the folder (no other .ino files)
3. Power cycle the ESP32 after uploading

### If enrollment always fails:
- This is **normal behavior** if no face is presented to the camera
- The timeout (result 0xD) is expected
- The important fix is that notifications are now handled properly

### If verification succeeds immediately:
- This means there are already enrolled users in the device
- This is actually good - it shows the device is working correctly

## Success Criteria ✅

The fixes are working correctly if you see:
1. **No "Unexpected message ID" errors**
2. **Face state notifications (📢) instead of errors**
3. **Clear error descriptions** instead of just hex codes
4. **Graceful handling** of unsupported commands
5. **Proper timeout handling** during enrollment/verification

The key improvement is that the system now properly handles the notification messages that were previously causing communication errors.
