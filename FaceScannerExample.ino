
// Example usage file (FaceScannerExample.ino)

#include "FaceScannerEnhanced.h"

FaceScannerEnhanced faceScanner;

void setup() {
  Serial.begin(115200);
  Serial.println("FaceScanner Enhanced Library Example");
  delay(2000);
  
  // Initialize the face scanner
  if (!faceScanner.begin()) {
      Serial.println("Failed to initialize FaceScanner");
      return;
  }
  
  // Wait for module to be ready
  delay(2000);
  
  // Reset the module
  if (faceScanner.reset()) {
      Serial.println("Module reset successful");
  }
  
  // Get firmware version
  char version[64];
  if (faceScanner.getVersion(version, sizeof(version))) {
      Serial.print("Firmware version: ");
      Serial.println(version);
  }
  
  // Get library version (may not be supported by all firmware versions)
  Serial.println("→ Getting library version");
  if (faceScanner.getLibraryVersion(version, sizeof(version))) {
      Serial.print("Library version: ");
      Serial.println(version);
  } else {
      Serial.println("ℹ Library version command not supported by this firmware");
  }

  // Get module status
  uint8_t status;
  if (faceScanner.getStatus(&status)) {
      Serial.print("Module status: ");
      Serial.println(status);
  }

  // Clear any existing users first to avoid enrollment conflicts
  Serial.println("→ Clearing existing users");
  faceScanner.deleteAllUsers(0); // Delete all normal users

  // Wait a moment for the operation to complete
  delay(1000);

  // Example: Enroll a user
  Serial.println("→ Starting user enrollment");
  EnrollResult enrollResult;
  if (faceScanner.enroll(1, "TestUser", FACE_DIRECTION_FRONT, 30, &enrollResult)) {
      Serial.print("✓ User enrolled with ID: ");
      Serial.println(enrollResult.userId);

      // Clean up allocated memory
      if (enrollResult.faceData) {
          free(enrollResult.faceData);
      }
  } else {
      Serial.println("✗ User enrollment failed - this is normal if no face is detected");
  }

  // Wait a moment for enrollment to be processed
  delay(2000);

  // Check if user was actually enrolled by getting user count
  Serial.println("→ Checking enrolled users");
  uint8_t userCount;
  if (faceScanner.getAllUserIds(0, nullptr, &userCount)) {
      Serial.print("📊 Total users enrolled: ");
      Serial.println(userCount);

      if (userCount > 0) {
          uint16_t* userIds = (uint16_t*)malloc(userCount * sizeof(uint16_t));
          if (userIds && faceScanner.getAllUserIds(1, userIds, &userCount)) {
              Serial.println("📊 Enrolled user IDs:");
              for (int i = 0; i < userCount; i++) {
                  Serial.print("  User ID: ");
                  Serial.println(userIds[i]);
              }
              free(userIds);
          }
      }
  } else {
      Serial.println("✗ Failed to get user count");
  }

  // Example: Verify a user - Try different verification parameters
  Serial.println("→ Starting user verification (attempt 1)");
  VerifyResult verifyResult;
  if (faceScanner.verify(1, 10, VERIFY_MODE_STANDARD, &verifyResult)) {
      Serial.print("✓ User verified: ");
      Serial.print(verifyResult.userName);
      Serial.print(" (ID: ");
      Serial.print(verifyResult.userId);
      Serial.println(")");
  } else {
      Serial.println("✗ User verification failed (attempt 1)");

      // Try alternative verification parameters
      Serial.println("→ Trying verification with different parameters (attempt 2)");
      delay(1000);
      if (faceScanner.verify(0, 15, VERIFY_MODE_STANDARD, &verifyResult)) {
          Serial.print("✓ User verified (attempt 2): ");
          Serial.print(verifyResult.userName);
          Serial.print(" (ID: ");
          Serial.print(verifyResult.userId);
          Serial.println(")");
      } else {
          Serial.println("✗ User verification failed (attempt 2) - this may be normal if no enrolled users or no face detected");
      }
  }
  
  // Example: Get all user IDs
//  uint8_t userCount;
  if (faceScanner.getAllUserIds(0, nullptr, &userCount)) {
      Serial.print("Total users: ");
      Serial.println(userCount);
      
      if (userCount > 0) {
          uint16_t* userIds = (uint16_t*)malloc(userCount * sizeof(uint16_t));
          if (userIds && faceScanner.getAllUserIds(1, userIds, &userCount)) {
              Serial.println("User IDs:");
              for (int i = 0; i < userCount; i++) {
                  Serial.print("  ");
                  Serial.println(userIds[i]);
              }
          }
          if (userIds) free(userIds);
      }
  }
}

void loop() {
  // Example: Listen for face detection notes
  uint8_t noteData[32];
  uint16_t noteSize;
  
  if (faceScanner.waitForNote(NID_FACE_STATE, noteData, &noteSize, 1000)) {
      FaceInfo faceInfo;
      if (faceScanner.parseFaceInfo(noteData, noteSize, &faceInfo)) {
          // Process face information
          switch (faceInfo.state) {
              case FACE_STATE_NORMAL:
                  Serial.println("Face detected - Normal");
                  break;
              case FACE_STATE_NOFACE:
                  Serial.println("No face detected");
                  break;
              case FACE_STATE_FAR:
                  Serial.println("Face too far");
                  break;
              case FACE_STATE_CLOSE:
                  Serial.println("Face too close");
                  break;
              default:
                  Serial.print("Face state: ");
                  Serial.println(faceInfo.state);
                  break;
          }
      }
  }
  
  delay(100);
}



// Additional utility functions for common operations

class FaceScannerUtils {
public:
  // Convert face state to human readable string
  static const char* faceStateToString(uint16_t state) {
      switch (state) {
          case FACE_STATE_NORMAL: return "Normal";
          case FACE_STATE_NOFACE: return "No Face";
          case FACE_STATE_TOOUP: return "Too Up";
          case FACE_STATE_TOODOWN: return "Too Down";
          case FACE_STATE_TOOLEFT: return "Too Left";
          case FACE_STATE_TOORIGHT: return "Too Right";
          case FACE_STATE_FAR: return "Too Far";
          case FACE_STATE_CLOSE: return "Too Close";
          case FACE_STATE_EYEBROW_OCCLUSION: return "Eyebrow Occluded";
          case FACE_STATE_EYE_OCCLUSION: return "Eye Occluded";
          case FACE_STATE_FACE_OCCLUSION: return "Face Occluded";
          case FACE_STATE_DIRECTION_ERROR: return "Direction Error";
          case FACE_STATE_EYE_CLOSE_STATUS_OPEN_EYE: return "Eyes Open";
          case FACE_STATE_EYE_CLOSE_STATUS: return "Eyes Closed";
          case FACE_STATE_EYE_CLOSE_UNKNOW_STATUS: return "Eye Status Unknown";
          case FACE_STATE_HAND_NORMAL: return "Hand Normal";
          case FACE_STATE_HAND_FAR: return "Hand Too Far";
          case FACE_STATE_HAND_CLOSE: return "Hand Too Close";
          case FACE_STATE_HAND_TOOUP: return "Hand Too Up";
          case FACE_STATE_HAND_TOODOWN: return "Hand Too Down";
          case FACE_STATE_HAND_TOOLEFT: return "Hand Too Left";
          case FACE_STATE_HAND_TOORIGHT: return "Hand Too Right";
          default: return "Unknown State";
      }
  }
  
  // Convert result code to human readable string
  static const char* resultCodeToString(uint8_t result) {
      switch (result) {
          case MR_SUCCESS: return "Success";
          case MR_REJECTED: return "Rejected";
          case MR_ABORTED: return "Aborted";
          case MR_FAILED4_CAMERA: return "Camera Failed";
          case MR_FAILED4_UNKNOWNREASON: return "Unknown Reason";
          case MR_FAILED4_INVALIDPARAM: return "Invalid Parameter";
          case MR_FAILED4_NOMEMORY: return "No Memory";
          case MR_FAILED4_UNKNOWNUSER: return "Unknown User";
          case MR_FAILED4_MAXUSER: return "Max Users Reached";
          case MR_FAILED4_FACEENROLLED: return "Face Already Enrolled";
          case MR_FAILED4_LIVENESSCHECK: return "Liveness Check Failed";
          case MR_FAILED4_TIMEOUT: return "Timeout";
          case MR_FAILED4_AUTHORIZATION: return "Authorization Failed";
          case MR_FAILED4_READ_FILE: return "Read File Failed";
          case MR_FAILED4_WRITE_FILE: return "Write File Failed";
          case MR_FAILED4_NO_ENCRYPT: return "No Encryption";
          case MR_FAILED4_NO_RGBIMAGE: return "No RGB Image";
          case MR_FAILED4_UNKNOWN_HANDUSER: return "Unknown Hand User";
          case MR_FAILED4_NOCAMERA: return "No Camera";
          case MR_FAILED4_HANDENROLLED: return "Hand Already Enrolled";
          default: return "Unknown Error";
      }
  }
  
  // Convert face direction to human readable string
  static const char* faceDirectionToString(uint8_t direction) {
      switch (direction) {
          case FACE_DIRECTION_FRONT: return "Front";
          case FACE_DIRECTION_UP: return "Up";
          case FACE_DIRECTION_DOWN: return "Down";
          case FACE_DIRECTION_LEFT: return "Left";
          case FACE_DIRECTION_RIGHT: return "Right";
          case FACE_DIRECTION_HAND: return "Hand";
          case FACE_DIRECTION_PICTURE: return "Picture";
          case FACE_DIRECTION_RENT: return "Rent";
          case FACE_DIRECTION_FACE_ONLY: return "Face Only";
          default: return "Unknown Direction";
      }
  }
  
  // Validate face position within acceptable bounds
  static bool isFacePositionValid(const FaceInfo& faceInfo, int imageWidth = 640, int imageHeight = 480) {
      // Check if face is within image bounds with some margin
      int margin = 50;
      
      if (faceInfo.left < margin || faceInfo.top < margin ||
          faceInfo.right < margin || faceInfo.bottom < margin) {
          return false;
      }
      
      // Check face size (distance)
      int faceWidth = imageWidth - faceInfo.left - faceInfo.right;
      int faceHeight = imageHeight - faceInfo.top - faceInfo.bottom;
      
      if (faceWidth < 100 || faceHeight < 100 || faceWidth > 400 || faceHeight > 400) {
          return false;
      }
      
      // Check face angles (in degrees, approximate)
      if (abs(faceInfo.yaw) > 30 || abs(faceInfo.pitch) > 20 || abs(faceInfo.roll) > 15) {
          return false;
      }
      
      return true;
  }
  
  // Calculate face quality score (0-100)
  static uint8_t calculateFaceQuality(const FaceInfo& faceInfo, int imageWidth = 640, int imageHeight = 480) {
      if (faceInfo.state != FACE_STATE_NORMAL) {
          return 0;
      }
      
      uint8_t score = 100;
      
      // Penalize for position
      int centerX = imageWidth / 2;
      int centerY = imageHeight / 2;
      int faceX = (imageWidth - faceInfo.left - faceInfo.right) / 2 + faceInfo.left;
      int faceY = (imageHeight - faceInfo.top - faceInfo.bottom) / 2 + faceInfo.top;
      
      int distanceFromCenter = sqrt(pow(faceX - centerX, 2) + pow(faceY - centerY, 2));
      score -= (distanceFromCenter * 20) / centerX; // Max 20 point penalty
      
      // Penalize for angles
      score -= (abs(faceInfo.yaw) * 30) / 90;    // Max 30 point penalty for yaw
      score -= (abs(faceInfo.pitch) * 20) / 90;  // Max 20 point penalty for pitch
      score -= (abs(faceInfo.roll) * 10) / 90;   // Max 10 point penalty for roll
      
      // Penalize for size (too small or too large)
      int faceWidth = imageWidth - faceInfo.left - faceInfo.right;
      int optimalWidth = imageWidth / 3; // Optimal face width is 1/3 of image
      int sizeDiff = abs(faceWidth - optimalWidth);
      score -= (sizeDiff * 20) / optimalWidth; // Max 20 point penalty
      
      return (score > 0) ? score : 0;
  }
};

// Debug helper macros
#define DEBUG_FACE_SCANNER 1

#if DEBUG_FACE_SCANNER
  #define FS_DEBUG(x) Serial.print("[FS] "); Serial.println(x)
  #define FS_DEBUG_F(x, y) Serial.print("[FS] "); Serial.printf(x, y)
#else
  #define FS_DEBUG(x)
  #define FS_DEBUG_F(x, y)
#endif

// Task-based wrapper for FreeRTOS integration
class FaceScannerTask {
private:
  FaceScannerEnhanced* scanner;
  TaskHandle_t taskHandle;
  QueueHandle_t commandQueue;
  QueueHandle_t responseQueue;
  
  struct Command {
      uint8_t type;
      void* data;
      uint16_t dataSize;
  };
  
  static void taskFunction(void* parameter) {
      FaceScannerTask* task = (FaceScannerTask*)parameter;
      task->run();
  }
  
  void run() {
      Command cmd;
      while (true) {
          if (xQueueReceive(commandQueue, &cmd, portMAX_DELAY)) {
              processCommand(cmd);
          }
      }
  }
  
  void processCommand(const Command& cmd) {
      // Process different command types
      switch (cmd.type) {
          case 1: // Verify
              // Handle verify command
              break;
          case 2: // Enroll
              // Handle enroll command
              break;
          // Add more command types as needed
      }
  }
  
public:
  FaceScannerTask(FaceScannerEnhanced* fs) : scanner(fs) {
      commandQueue = xQueueCreate(10, sizeof(Command));
      responseQueue = xQueueCreate(10, sizeof(void*));
  }
  
  bool start(const char* taskName = "FaceScannerTask", uint32_t stackSize = 4096, UBaseType_t priority = 1) {
      return xTaskCreate(taskFunction, taskName, stackSize, this, priority, &taskHandle) == pdPASS;
  }
  
  void stop() {
      if (taskHandle) {
          vTaskDelete(taskHandle);
          taskHandle = nullptr;
      }
  }
  
  bool sendCommand(uint8_t type, void* data = nullptr, uint16_t dataSize = 0) {
      Command cmd = {type, data, dataSize};
      return xQueueSend(commandQueue, &cmd, pdMS_TO_TICKS(1000)) == pdPASS;
  }
};

